-- =====================================================
-- Fix Driver Bookings Function
-- This script ensures the get_driver_bookings function works correctly
-- =====================================================

-- Drop the old function if it exists
DROP FUNCTION IF EXISTS get_driver_bookings(uuid, text);

-- Create the corrected get_driver_bookings function
CREATE OR REPLACE FUNCTION get_driver_bookings(
    p_driver_id UUID,
    p_status TEXT DEFAULT NULL
) RETURNS SETOF bookings AS $$
BEGIN
    RETURN QUERY
    SELECT *
    FROM bookings
    WHERE driver_id = p_driver_id
      AND (p_status IS NULL OR status = p_status)
    ORDER BY created_at DESC;
END;
$$ LANGUAGE plpgsql STABLE SECURITY DEFINER;

-- Grant execute permission to authenticated users
GRANT EXECUTE ON FUNCTION get_driver_bookings(UUID, TEXT) TO authenticated;

-- Add comment for documentation
COMMENT ON FUNCTION get_driver_bookings(UUID, TEXT) IS 'Returns all bookings for a driver with optional status filter';

-- Test the function (uncomment to test)
-- SELECT * FROM get_driver_bookings('test-driver-id'::uuid, 'pending');
