import 'package:flutter/foundation.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'lib/services/booking_service.dart';

/// Debug script to check booking data and identify issues
void main() async {
  if (kDebugMode) {
    print('🔍 Debug: Checking booking data...\n');
  }

  // Initialize Supabase (you'll need to add your actual URL and key)
  await Supabase.initialize(
    url: 'YOUR_SUPABASE_URL',
    anonKey: 'YOUR_SUPABASE_ANON_KEY',
  );

  final client = Supabase.instance.client;

  // Test 1: Check if there are any bookings at all
  if (kDebugMode) {
    print('🔍 Test 1: Checking all bookings in database...');
  }
  try {
    final allBookings = await client.from('bookings').select('*');
    if (kDebugMode) {
      print('✅ Total bookings in database: ${allBookings.length}');
      if (allBookings.isNotEmpty) {
        print('   Sample booking: ${allBookings.first}');
      }
    }
  } catch (e) {
    if (kDebugMode) {
      print('❌ Error fetching all bookings: $e');
    }
  }

  // Test 2: Check pending bookings specifically
  if (kDebugMode) {
    print('\n🔍 Test 2: Checking pending bookings...');
  }
  try {
    final pendingBookings = await client
        .from('bookings')
        .select('*')
        .eq('status', 'pending');
    if (kDebugMode) {
      print('✅ Pending bookings: ${pendingBookings.length}');
      if (pendingBookings.isNotEmpty) {
        print('   Sample pending booking: ${pendingBookings.first}');
      }
    }
  } catch (e) {
    if (kDebugMode) {
      print('❌ Error fetching pending bookings: $e');
    }
  }

  // Test 3: Check bookings with joins
  if (kDebugMode) {
    print('\n🔍 Test 3: Checking bookings with trip and passenger data...');
  }
  try {
    final bookingsWithJoins = await client
        .from('bookings')
        .select('''
          *,
          trip:trips(*),
          passenger:users!passenger_id(*)
        ''')
        .eq('status', 'pending');
    if (kDebugMode) {
      print('✅ Bookings with joins: ${bookingsWithJoins.length}');
      if (bookingsWithJoins.isNotEmpty) {
        final sample = bookingsWithJoins.first;
        print('   Sample booking ID: ${sample['id']}');
        print('   Trip data present: ${sample['trip'] != null}');
        print('   Passenger data present: ${sample['passenger'] != null}');
      }
    }
  } catch (e) {
    if (kDebugMode) {
      print('❌ Error fetching bookings with joins: $e');
    }
  }

  // Test 4: Test BookingService.fetchDriverBookings
  if (kDebugMode) {
    print('\n🔍 Test 4: Testing BookingService.fetchDriverBookings...');
  }
  try {
    // Use a sample driver ID - replace with actual driver ID from your database
    final driverBookings = await BookingService.fetchDriverBookings(
      driverId: 'REPLACE_WITH_ACTUAL_DRIVER_ID',
      status: 'pending',
    );
    if (kDebugMode) {
      print('✅ BookingService.fetchDriverBookings works: ${driverBookings.length} bookings');
      if (driverBookings.isNotEmpty) {
        print('   First booking: ${driverBookings.first.id}');
        print('   Passenger: ${driverBookings.first.passenger?.fullName}');
        print('   Trip: ${driverBookings.first.trip?.fromCity} -> ${driverBookings.first.trip?.toCity}');
      }
    }
  } catch (e) {
    if (kDebugMode) {
      print('❌ BookingService.fetchDriverBookings failed: $e');
    }
  }

  // Test 5: Check if there are any drivers in the system
  if (kDebugMode) {
    print('\n🔍 Test 5: Checking drivers in system...');
  }
  try {
    final drivers = await client
        .from('users')
        .select('id, full_name, role')
        .eq('role', 'driver');
    if (kDebugMode) {
      print('✅ Drivers in system: ${drivers.length}');
      if (drivers.isNotEmpty) {
        print('   Sample driver: ${drivers.first}');
      }
    }
  } catch (e) {
    if (kDebugMode) {
      print('❌ Error fetching drivers: $e');
    }
  }

  if (kDebugMode) {
    print('\n🎯 Debug completed!');
    print('If no pending bookings are found, you may need to create test data.');
    print('If bookings exist but the UI shows empty, check the data parsing logic.');
  }
}
