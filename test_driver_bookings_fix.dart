import 'package:flutter/foundation.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'lib/services/booking_service.dart';

/// Test script to verify driver bookings functionality
/// Run this to test if the fixes work correctly
void main() async {
  print('🧪 Testing Driver Bookings Fix...\n');

  // Initialize Supabase (you'll need to add your actual URL and key)
  await Supabase.initialize(
    url: 'YOUR_SUPABASE_URL',
    anonKey: 'YOUR_SUPABASE_ANON_KEY',
  );

  final client = Supabase.instance.client;

  // Test 1: Check if get_driver_bookings function exists
  print('🔍 Test 1: Testing get_driver_bookings function...');
  try {
    final result = await client.rpc('get_driver_bookings', params: {
      'p_driver_id': '00000000-0000-0000-0000-000000000000',
      'p_status': 'pending',
    });
    print('✅ get_driver_bookings function works: ${result.length} results');
  } catch (e) {
    print('❌ get_driver_bookings function failed: $e');
  }

  // Test 2: Test BookingService.fetchDriverBookings method
  print('\n🔍 Test 2: Testing BookingService.fetchDriverBookings...');
  try {
    final bookings = await BookingService.fetchDriverBookings(
      driverId: '00000000-0000-0000-0000-000000000000',
      status: 'pending',
    );
    print('✅ fetchDriverBookings method works: ${bookings.length} bookings');
  } catch (e) {
    print('❌ fetchDriverBookings method failed: $e');
  }

  // Test 3: Test direct query fallback
  print('\n🔍 Test 3: Testing direct query fallback...');
  try {
    final result = await client
        .from('bookings')
        .select('''
          *,
          trip:trips(*),
          passenger:users!passenger_id(*)
        ''')
        .eq('driver_id', '00000000-0000-0000-0000-000000000000')
        .eq('status', 'pending')
        .order('created_at', ascending: false);
    print('✅ Direct query works: ${result.length} results');
  } catch (e) {
    print('❌ Direct query failed: $e');
  }

  print('\n🎯 Test completed!');
  print('If all tests pass, the driver bookings should work correctly.');
  print('If any test fails, check the Supabase function and table structure.');
}
